PROJECT DESCRIPTION & Requirements:

Crowd-Funding console app 
Crowdfunding is the practice of funding a project or venture by raising small 
amounts of money from a large number of people, typically via the Internet. 
Crowdfunding is a form of crowdsourcing and alternative finance. In 2015, 
over US$34 billion was raised worldwide by crowdfunding. (From Wikipedia) 
The aim of the project: Create a console app to start fundraise projects. 
The app should include the following features: 
1 - Authentication System: 
• Registration: 
• First name 
• Last name 
• Email 
• Password 
• Confirm password 
• Mobile phone [validated against Egyptian phone numbers]   
• Login 
• The user should be able to login after activation using his email 
and password  
2 - Projects: 
• The user can create a project fund raise campaign which contains: 
• Title 
• Details 
• Total target (i.e 250000 EGP) 
• Set start/end time for the campaign (validate the date formula) 
• User can view all projects 
• User can edit his own projects 
• User can delete his own project 
• User can search for a project using date (bonus) 
Similar Projects to get some inspiration :)  
https://www.gofundme.com https://www.kickstarter.com   https://www.crowdfunding.com




STARTING WITH STREAMLIT:
Dear Dev<PERSON><PERSON>, 
If you want to start learning more about streamlit, please here some resources:
Documentation: https://docs.streamlit.io/
How your session works: https://docs.streamlit.io/develop/api-reference/caching-and-state/st.session_state 
using forms: https://docs.streamlit.io/develop/concepts/architecture/forms
using rerun: https://docs.streamlit.io/develop/api-reference/execution-flow/st.rerun works as a refresh button instead of onchange and onclick
using expander: https://docs.streamlit.io/develop/api-reference/layout/st.expander 3azmaaaaaaaaaaaaa
using columns: https://docs.streamlit.io/develop/api-reference/layout/st.columns
using delete: https://docs.streamlit.io/develop/api-reference/caching-and-state/st.session_state#delete-items
